# SingleSignOn-ACE - Windows Server インストールガイド

本ドキュメントは、SingleSignOn-ACE（ACE専用シングルサインオンAPIサーバー）をWindows Serverに新規インストールする際の手順を説明します。

## 目次
1. [システム要件](#システム要件)
2. [事前準備](#事前準備)
3. [Node.js環境構築](#nodejs環境構築)
4. [MySQL環境構築](#mysql環境構築)
5. [アプリケーションインストール](#アプリケーションインストール)
6. [データベース設定](#データベース設定)
7. [アプリケーション設定](#アプリケーション設定)
8. [Windows サービス設定](#windowsサービス設定)
9. [アプリケーション起動](#アプリケーション起動)
10. [監視設定](#監視設定)
11. [セキュリティ設定](#セキュリティ設定)
12. [トラブルシューティング](#トラブルシューティング)

## システム要件

### 最小要件
- **OS**: Windows Server 2016 以上
- **メモリ**: 4GB RAM 以上
- **ディスク容量**: 10GB 以上の空き容量
- **ネットワーク**: インターネット接続（初回セットアップ時）

### 推奨要件
- **OS**: Windows Server 2019/2022
- **メモリ**: 8GB RAM 以上
- **ディスク容量**: 20GB 以上の空き容量
- **CPU**: 4コア以上

## 事前準備

### 1. Windows機能の有効化
以下のWindows機能を有効にしてください：
- IIS (Web Server) ※必要に応じて
- .NET Framework 4.7.2 以上
- Active Directory 連携機能（SSO用）

### 2. ファイアウォール設定
以下のポートを開放してください：
- **30002**: SingleSignOn-ACE サーバー（デフォルト）
- **3306**: MySQL データベース（ローカル接続の場合）
- **389**: LDAP/AD認証（SSO用）
- **636**: LDAPS（SSL暗号化LDAP）

## Node.js環境構築

### 1. Node.jsのインストール
1. [Node.js公式サイト](https://nodejs.org/)から **LTS版 (v12.x以上)** をダウンロード
2. インストーラーを実行し、以下のオプションを選択：
   - ☑ Add to PATH
   - ☑ npm package manager

### 2. インストール確認
PowerShellまたはコマンドプロンプトで以下を実行：
```powershell
node --version
npm --version
```

## MySQL環境構築

### 1. MySQLのインストール
1. [MySQL公式サイト](https://dev.mysql.com/downloads/mysql/)から **MySQL 8.0** をダウンロード
2. MySQL Installerを実行し、以下を選択：
   - **Server only** または **Developer Default**
   - **MySQL Server 8.0**

### 2. MySQL設定
```sql
-- データベースの作成
CREATE DATABASE rtsc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ユーザーの作成
CREATE USER 'root'@'localhost' IDENTIFIED BY 'root';
GRANT ALL PRIVILEGES ON rtsc.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 3. MySQL サービス起動
```powershell
# サービスの確認
Get-Service MySQL*

# サービスの開始
Start-Service MySQL80
```

## アプリケーションインストール

### 1. アプリケーションファイルの配置
SingleSignOn-ACEを以下のディレクトリに配置：
```
C:\inetpub\wwwroot\singlesignon-ace\
```

### 2. 依存関係のインストール
アプリケーションルートディレクトリで実行：
```powershell
cd C:\inetpub\wwwroot\singlesignon-ace
npm install
```

## データベース設定

### 1. データベース接続設定の編集
`db/dbconnpool.js` を環境に合わせて編集：

```javascript
var mysql = require('mysql');
module.exports = (function(){
    var pool = mysql.createPool({
        host: 'localhost',           // MySQLサーバーのホスト
        user: 'root',               // MySQLユーザー名
        password: 'your_password',   // MySQLパスワード
        database: 'rtsc',           // データベース名
        port: '3306',               // MySQLポート
        connectionLimit: 10,        // 接続プール数
        acquireTimeout: 60000,      // 接続タイムアウト
        timeout: 60000,             // クエリタイムアウト
        reconnect: true             // 自動再接続
    });
    
    pool.on('connection', function(connection){
        connection.query('SET SESSION auto_increment_increment=1');
    });
    
    return function(){      
        return pool;
    }
})();
```

### 2. データベーススキーマの作成
ACE専用SSO機能に必要なテーブルを作成してください（具体的なSQLスクリプトは別途用意）。

## アプリケーション設定

### 1. ポート設定の確認
`bin/www` ファイルでポート設定を確認（デフォルト: 30002）：
```javascript
var port = normalizePort(process.env.PORT || '30002');
```

### 2. 環境変数の設定
本番環境用の環境変数を設定：
```powershell
# システム環境変数の設定
[Environment]::SetEnvironmentVariable("NODE_ENV", "production", "Machine")
[Environment]::SetEnvironmentVariable("PORT", "30002", "Machine")

# ACE専用SSO設定
[Environment]::SetEnvironmentVariable("ACE_SSO_DOMAIN", "your-ace-domain.com", "Machine")
[Environment]::SetEnvironmentVariable("AD_SERVER", "your-ad-server.com", "Machine")
```

### 3. ログディレクトリの作成
```powershell
mkdir C:\Logs\SingleSignOn-ACE
mkdir C:\Windows\System32\GroupPolicy\Machine\Scripts\SingleSingnOn-ACE
```

## Windows サービス設定

### 1. PM2を使用したサービス化（推奨）
```powershell
# PM2のインストール
npm install -g pm2
npm install -g pm2-windows-service
```

`ecosystem.config.js` を作成：
```javascript
module.exports = {
  apps: [{
    name: 'sso-ace-api',
    script: './bin/www',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 30002
    },
    log_file: 'C:/Logs/SingleSignOn-ACE/combined.log',
    out_file: 'C:/Logs/SingleSignOn-ACE/out.log',
    error_file: 'C:/Logs/SingleSignOn-ACE/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
};
```

### 2. サービス登録
```powershell
# PM2でアプリケーション起動
pm2 start ecosystem.config.js

# 設定の保存
pm2 save

# Windows サービスとして登録
pm2-service-install
```

## アプリケーション起動

### 1. 手動起動（開発・テスト用）
```powershell
# 直接起動
npm start

# または付属のバッチファイル使用
ExpressServerStart.bat
```

### 2. サービス起動（本番用）
```powershell
# PM2サービスの開始
pm2 start sso-ace-api

# サービス状態確認
pm2 status
pm2 logs sso-ace-api
```

### 3. 起動確認
ブラウザまたはcurlでアクセス確認：
```
http://localhost:30002/
```

## 監視設定

### 1. Windows タスクスケジューラ設定
付属の監視スクリプト `HQACE_SSO_HTTPタスク監視.bat` を使用してタスクスケジューラに登録：

1. **タスクスケジューラ** を開く
2. **基本タスクの作成** を選択
3. 以下の設定で作成：
   - **名前**: `[本部ACEシングルサインオン]APIサーバー監視タスク`
   - **トリガー**: 毎日 または 起動時
   - **操作**: プログラムの開始
   - **プログラム**: `C:\inetpub\wwwroot\singlesignon-ace\HQACE_SSO_HTTPタスク監視.bat`

### 2. ログ監視
以下のログファイルを定期的に監視：
- `Check.log` - タスク監視ログ
- `debug.log` - アプリケーションデバッグログ
- PM2ログファイル群

### 3. ヘルスチェック
定期的にAPIエンドポイントの応答を確認：
```powershell
# 基本ヘルスチェック
Invoke-RestMethod -Uri "http://localhost:30002/" -Method GET

# ACE専用SSO機能確認
Invoke-RestMethod -Uri "http://localhost:30002/login" -Method GET
```

## セキュリティ設定

### 1. JWT設定の強化
本番環境では強力な秘密鍵を設定：
```javascript
// 例: config/auth.js
module.exports = {
    jwtSecret: process.env.JWT_SECRET || 'your-ace-specific-secret-key',
    jwtExpiration: '24h',
    refreshTokenExpiration: '7d',
    aceSpecificClaims: {
        issuer: 'ace-sso-server',
        audience: 'ace-applications'
    }
};
```

### 2. CORS設定の調整
`app.js` のCORS設定をACE専用に調整：
```javascript
var allowCrossDomain = function (req, res, next) {
  res.header('Access-Control-Allow-Origin', 'https://ace.your-domain.com');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE');
  res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
};
```

### 3. HTTPS化の設定
本番環境では必ずHTTPS化を実装してください。SSL証明書の設定が必要です。

### 4. Active Directory連携設定
ACE専用SSO機能のためのAD連携設定：
```javascript
// 例: ACE専用AD設定
const aceAdConfig = {
    url: 'ldap://your-ad-server.com:389',
    baseDN: 'dc=your-domain,dc=com',
    username: '<EMAIL>',
    password: 'ace-service-account-password',
    aceSpecificGroups: ['ACE-Users', 'ACE-Admins']
};
```

## トラブルシューティング

### よくあるエラーと対処法

#### 1. ポート30002が使用中
```powershell
# ポート使用状況確認
netstat -ano | findstr :30002

# プロセス終了
taskkill /PID <プロセスID> /F
```

#### 2. MySQL接続エラー
```powershell
# MySQL サービス状態確認
Get-Service MySQL80

# MySQL接続テスト
mysql -u root -p -h localhost
```

#### 3. ACE専用SSO認証エラー
- Active Directory接続設定を確認
- ACE専用ユーザーグループの設定確認
- LDAP/AD認証情報の確認
- ネットワーク接続（ポート389/636）の確認

#### 4. JWT トークンエラー
- ACE専用秘密鍵の設定確認
- トークン有効期限の確認
- ACE特有のクレーム設定確認

### APIエンドポイント確認
以下のエンドポイントが正常に応答することを確認：
- `GET /` - 基本接続確認
- `POST /login` - ACE専用SSO認証確認
- `GET /api` - API基本確認
- `GET /users` - ACEユーザー管理確認
- `GET /common` - 共通サービス確認

## ACEエコシステム内での位置づけ

### 連携システム
SingleSignOn-ACEは以下のシステムと連携します：

1. **ACEアプリケーション** (ポート4203)
   - 主要フロントエンドアプリケーション
   - SSO認証の受益者

2. **ACE-Menu-API** (ポート3000)
   - バックエンドAPI機能
   - 認証トークン検証

3. **Scanless** (ポート4202)
   - スキャンレス在庫管理
   - SSO統合認証

4. **SingleSignOn-Scanless** (ポート30003)
   - スキャンレス専用SSO
   - 役割分離による特化サービス

### ACE専用機能の特徴
- **ACE特化認証**: ACEアプリケーション専用の認証フロー
- **統合ユーザー管理**: ACE関連システム全体のユーザー管理
- **セッション管理**: ACE専用のセッション管理機能
- **権限管理**: ACE固有の権限・ロール管理

## 保守・運用

### 定期メンテナンス
1. **毎日**: 
   - ログファイルサイズの確認
   - ACE専用SSO認証状況の確認
2. **週次**: 
   - データベース接続状況の確認
   - AD連携状況の確認
3. **月次**: 
   - Node.jsとnpmのアップデート確認
   - MySQL のパフォーマンス確認
   - SSL証明書の有効期限確認
4. **四半期**: 依存関係の脆弱性チェック
   ```powershell
   npm audit
   npm audit fix
   ```

### バックアップ
以下を定期的にバックアップ：
- アプリケーション全体
- `db/dbconnpool.js` 設定ファイル
- `ecosystem.config.js` PM2設定
- MySQL データベース
- ACE専用SSO設定ファイル
- SSL証明書

### パフォーマンス監視
```powershell
# CPU・メモリ使用率監視
Get-Process -Name "node" | Select-Object Name, CPU, WorkingSet

# ネットワーク接続監視
netstat -an | findstr :30002

# ACE専用認証レスポンス監視
Invoke-RestMethod -Uri "http://localhost:30002/login" -Method GET -TimeoutSec 5
```

## 連絡先
技術的な問題については、システム管理者またはIT部門にお問い合わせください。

## 補足
このアプリケーションは、ACEシステム専用のシングルサインオン（SSO）機能を提供するAPIサーバーです。ACEエコシステム内の他のシステムとは別ポート（30002）で動作し、ACE特有の認証要件に特化した設計となっています。セキュリティ要件が高いため、本番環境では必ず適切なセキュリティ設定を実装してください。 