# Scanless アプリケーション - Windows Server インストールガイド

本ドキュメントは、Scanless（スキャンレス在庫管理 Angular フロントエンド）をWindows Serverに新規インストールする際の手順を説明します。

## 目次
1. [システム要件](#システム要件)
2. [事前準備](#事前準備)
3. [Node.js環境構築](#nodejs環境構築)
4. [アプリケーションインストール](#アプリケーションインストール)
5. [設定変更](#設定変更)
6. [バックエンドサーバー設定](#バックエンドサーバー設定)
7. [アプリケーション起動](#アプリケーション起動)
8. [統合システム連携](#統合システム連携)
9. [トラブルシューティング](#トラブルシューティング)

## システム要件

### 最小要件
- **OS**: Windows Server 2016 以上
- **メモリ**: 4GB RAM 以上
- **ディスク容量**: 5GB 以上の空き容量
- **ネットワーク**: インターネット接続（初回セットアップ時）

### 推奨要件
- **OS**: Windows Server 2019/2022
- **メモリ**: 8GB RAM 以上
- **ディスク容量**: 10GB 以上の空き容量
- **CPU**: 2コア以上

## 事前準備

### 1. Windows機能の有効化
以下のWindows機能を有効にしてください：
- IIS (Web Server) ※プロキシ設定で必要な場合
- .NET Framework 4.7.2 以上

### 2. ファイアウォール設定
以下のポートを開放してください：
- **4202**: Scanlessアプリケーション（デフォルト）
- **3000**: バックエンドAPI通信（ACE-Menu-API & SingleSignOn-Scanless）
- **8000**: 開発環境用APIベース（environment.ts設定）

**注意**: SingleSignOn-ScanlessサーバーはACE-Menu-APIと同じポート3000を使用します。

## Node.js環境構築

### 1. Node.jsのインストール
1. [Node.js公式サイト](https://nodejs.org/)から **LTS版 (v8.x以上、推奨v10.x)** をダウンロード
   - **注意**: Angular 5.2.0との互換性を考慮し、Node.js v8.x～v10.x を推奨
2. インストーラーを実行し、以下のオプションを選択：
   - ☑ Add to PATH
   - ☑ npm package manager

### 2. インストール確認
PowerShellまたはコマンドプロンプトで以下を実行：
```powershell
node --version
npm --version
```

### 3. Angular CLIのインストール
```powershell
npm install -g @angular/cli@1.7.4
```
**注意**: このバージョンはAngular 5.2.0と互換性があります。

### 4. TypeScriptのインストール
```powershell
npm install -g typescript@2.5.3
```
**注意**: このバージョンはプロジェクトの設定と一致しています。

## アプリケーションインストール

### 1. アプリケーションファイルの配置
Scanlessアプリケーションを以下のディレクトリに配置：
```
C:\inetpub\wwwroot\scanless\
```

### 2. 依存関係のインストール
アプリケーションルートディレクトリで実行：
```powershell
cd C:\inetpub\wwwroot\scanless
npm install
```

**注意**: `package-lock.json`が存在するため、正確なバージョンの依存関係がインストールされます。

## 設定変更

### 1. proxy.conf.json の設定
バックエンドサーバーのIPアドレスとポートに合わせて `proxy.conf.json` を編集：

**重要**: 実際のproxy.conf.jsonには400以上のAPIエンドポイントが定義されています。主要な設定例：

```json
{
    "/api/*": {
        "target": "http://[バックエンドサーバーIP]:3000/common",
        "secure": false,
        "changeOrigin": true
    },
    "/apiservice.svc/*": {
        "target": "http://[バックエンドサーバーIP]:3000/api",
        "secure": false,
        "changeOrigin": true,
        "pathRewrite": {
            "^/ace": ""
        }
    },
    "/GetCertification/auth": {
        "target": "http://[バックエンドサーバーIP]:3000/api",
        "secure": false
    },
    "/login/GetAuth": {
        "target": "http://[SSOサーバーIP]:3000",
        "secure": false
    }
}
```

**注意**:
- SSOサーバーは実際にはポート3000を使用（30003ではない）
- スキャンレス関連APIは `/api/GetScanless*` パターンで定義
- 認証関連APIは複数のエンドポイントに分散

### 2. 環境設定ファイルの編集
`src/environments/environment.prod.ts` を本番環境用に設定：

```typescript
export const environment = {
  production: true,
  defaultLang: 'ja-JP_dotst',
  apiBase: '/ace/API/'
};
```

`src/environments/environment.ts` （開発環境）の主要設定項目：

```typescript
export const environment = {
  production: false,
  defaultLang: 'ja-JP_dotst',
  apiBase: 'http://localhost:8000/',
  SystemKbn: '22',
  ApiKey: 'PROD_ADMS_DBS_8341E93326A2A9BA2EC51245D243F67D',
  CountryCd: 'JPN',
  CompanyCd: '0001',
  DateFormatString: '/',
  StatusCodeOK: '0000',
  ExecUser: "005000",
  PdfFileBaseUrl: "/output/",
  ConfigFile: "assets/config/config.json"
};
```

**重要な設定項目**:
- `SystemKbn`: システム区分（'22'）
- `ApiKey`: API認証キー（本番環境用）
- `CountryCd`: 国コード（'JPN'）
- `CompanyCd`: 会社コード（'0001'）
- `StatusCodeOK`: 正常ステータスコード（'0000'）

### 3. ベースHREF設定の確認
`package.json` のスクリプト設定を確認：
```json
{
  "scripts": {
    "start": "ng serve --port 4202 --base-href /ace --proxy-config proxy.conf.json",
    "build": "ng build --output-path=./dist/ace --base-href /ace/"
  }
}
```

`.angular-cli.json` の設定確認：
```json
{
  "apps": [{
    "root": "src",
    "outDir": "dist",
    "index": "index.html",
    "main": "main.ts"
  }]
}
```

## バックエンドサーバー設定

### 必要なバックエンドサービス
proxy.conf.jsonの分析により、以下のバックエンドサービスが必要です：

#### 1. 在庫管理API (ポート3000)
**スキャンレス機能** (`/api/GetScanless*` パターン):
- `/api/GetScanlessInstListForInstReg` - スキャンレス指示一覧取得
- `/api/GetScanlessInstInfo` - スキャンレス指示情報取得
- `/api/GetScanlessInstItemList` - スキャンレス指示商品一覧取得
- `/api/GetScanlessInstShippingStatus` - スキャンレス指示出荷状況取得
- `/api/GetScanlessInstDetails` - スキャンレス指示詳細取得
- `/api/ScanlessInstZeroCommit` - スキャンレス指示ゼロコミット
- `/api/RegScanlessInstInfo` - スキャンレス指示情報登録
- `/api/InsWrkScanlessInstItem` - スキャンレス指示商品登録
- `/api/UpdWrkScanlessInstItemImpChk` - スキャンレス指示商品重要チェック更新
- `/api/GetWrkScanlessInstItemErrInfo` - スキャンレス指示商品エラー情報取得

**在庫管理機能**:
- `/api/GetInventory*` - 在庫関連API群
- `/api/GetActualInventory*` - 実在庫関連API群
- `/api/InsInventory*` - 在庫登録API群
- `/api/UpdInventory*` - 在庫更新API群

**入出荷管理機能**:
- `/api/GetArrivalPlan*` - 入荷予定API群
- `/api/GetShipping*` - 出荷関連API群
- `/api/UpdArrival*` - 入荷更新API群
- `/api/InsWrkStk*` - 在庫ワーク登録API群

#### 2. 認証・共通サービス (ポート3000)
- `/api/GetEmployee*` - 従業員情報
- `/api/GetStore*` - 店舗情報
- `/api/GetDivision*` - 部門情報
- `/GetCertification/auth` - 認証サービス

#### 3. SingleSignOn連携 (ポート3000)
- `/login/GetAuth` - SSO認証
- `/GetCertification/auth` - 認証サービス
- `/GetCertification/test` - 認証テスト

### バックエンドサーバー要件
- **ACE-Menu-API**: ポート3000でリッスンするAPIサーバー
- **SingleSignOn-Scanless**: ポート3000でリッスンするSSOサーバー（30003ではない）
- 以下のエンドポイントを提供：
  - `/api/*` - REST API（主にcommonサービス経由）
  - `/apiservice.svc/*` - WCFサービス
  - `/common/*` - 共通サービス（在庫管理、スキャンレス機能）
  - `/login/*` - 認証サービス
  - `/users/*` - ユーザー管理サービス

**重要**: proxy.conf.jsonの分析により、実際のSSOサーバーはポート3000を使用しています。

## アプリケーション起動

### 1. 開発モードでの起動
```powershell
npm start
```
または付属のバッチファイルを使用：
```powershell
Start.bat
```

### 2. 本番ビルドと起動
```powershell
# ビルド（本番環境用）
npm run build

# ビルド結果確認
# dist/ace フォルダにビルド成果物が生成される

# 本番用Webサーバーでホスト (例: http-server)
npm install -g http-server
http-server dist/ace -p 4202

# または、IISでホスティング
# dist/ace フォルダをIISの仮想ディレクトリとして設定
```

**注意**: 本番ビルド時はproxy.conf.jsonは使用されません。代わりにIISやNginxでリバースプロキシを設定してください。

### 3. Windows サービスとしての起動 (推奨)
本番環境では[PM2](https://pm2.keymetrics.io/)またはWindows Serviceを使用：

`ecosystem.config.js` を作成：
```javascript
module.exports = {
  apps: [{
    name: 'scanless-app',
    script: 'node_modules/@angular/cli/bin/ng',
    args: 'serve --port 4202 --base-href /ace --proxy-config proxy.conf.json',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production'
    },
    log_file: 'C:/Logs/Scanless/combined.log',
    out_file: 'C:/Logs/Scanless/out.log',
    error_file: 'C:/Logs/Scanless/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
};
```

```powershell
# PM2のインストール
npm install -g pm2
npm install -g pm2-windows-service

# サービス登録
pm2 start ecosystem.config.js
pm2 save
pm2-service-install
```

## 統合システム連携

### ACEエコシステム内での位置づけ
Scanlessは以下のシステムと連携します：

1. **ACEアプリケーション** (ポート4203)
   - 基本在庫管理機能
   - 共通ユーザー認証

2. **ACE-Menu-API** (ポート3000)
   - スキャンレス在庫管理API
   - 入出荷管理API
   - マスターデータAPI

3. **SingleSignOn-Scanless** (ポート3000)
   - SSO認証機能
   - スキャンレス統合認証
   - Express.jsベースのNode.jsサーバー

### スキャンレス機能の特徴
- **バーコードスキャンレス**: 商品コード手入力による在庫管理
- **リアルタイム在庫更新**: 即座にシステム在庫を反映
- **入出荷連携**: 入荷・出荷業務との統合
- **差異管理**: 理論在庫と実在庫の差異分析

## アクセス確認

アプリケーション起動後、以下のURLでアクセス：
- **開発環境**: `http://localhost:4202/ace`
- **本番環境**: `http://[サーバーIP]:4202/ace`

## トラブルシューティング

### よくあるエラーと対処法

#### 1. `npm install` でエラーが発生する場合
```powershell
# キャッシュクリア
npm cache clean --force

# node_modulesフォルダを削除して再インストール
rmdir /s node_modules
npm install
```

#### 2. ポート4202が使用中の場合
```powershell
# 異なるポートでの起動
ng serve --port 4204 --base-href /ace --proxy-config proxy.conf.json
```

#### 3. バックエンド接続エラーの場合
- `proxy.conf.json`のtarget URLを確認
- ACE-Menu-APIサーバー（ポート3000）が起動していることを確認
- SingleSignOn-Scanlessサーバー（ポート3000）が起動していることを確認
- ファイアウォール設定を確認
- 開発環境では `environment.ts` の `apiBase` 設定を確認

#### 4. ビルドエラーが発生する場合
```powershell
# TypeScript設定の確認
ng build --verbose

# 依存関係の再インストール（Windows）
rmdir /s node_modules
del package-lock.json
npm install

# または（PowerShell）
Remove-Item -Recurse -Force node_modules
Remove-Item package-lock.json
npm install
```

#### 5. Angular CLIバージョン互換性エラーの場合
```powershell
# 現在のAngular CLIバージョン確認
ng version

# 正しいバージョンの再インストール
npm uninstall -g @angular/cli
npm install -g @angular/cli@1.7.4
```

#### 6. スキャンレス機能エラーの場合
- スキャンレスAPI（`/api/GetScanless*`）の応答確認
- データベース内のスキャンレステーブルの確認
- 商品マスターデータの整合性確認
- 環境変数（SystemKbn, ApiKey, CompanyCd等）の設定確認

#### 7. 認証エラーの場合
- `environment.ts` の `ApiKey` 設定確認
- SingleSignOn-Scanlessサーバーの起動状況確認
- セッションストレージの状態確認
```powershell
# ブラウザの開発者ツールでセッション確認
# Application > Session Storage > token の存在確認
```

### 機能確認
以下の機能が正常に動作することを確認：

#### スキャンレス機能
- スキャンレス指示一覧表示
- 商品コード手入力による在庫登録
- リアルタイム在庫数量更新
- スキャンレス指示完了処理

#### 在庫管理機能
- 在庫一覧表示
- 実在庫登録・更新
- 棚卸処理
- 在庫差異分析

#### 入出荷管理機能
- 入荷予定一覧
- 出荷指示処理
- 入出荷実績登録

## 保守・運用

### 定期メンテナンス
1. **月次**: Node.jsとnpmのアップデート確認
2. **四半期**: 
   - 依存関係の脆弱性チェック
   ```powershell
   npm audit
   ```
   - スキャンレスデータの整合性チェック
3. **年次**: Angular CLIとフレームワークのアップデート

### バックアップ
以下のファイル・フォルダを定期的にバックアップ：
- アプリケーション全体
- `proxy.conf.json`
- `src/environments/`
- カスタム設定ファイル

### パフォーマンス監視
```powershell
# アプリケーション応答時間確認
Invoke-RestMethod -Uri "http://localhost:4202/ace" -Method GET

# スキャンレスAPI応答確認（認証が必要）
$headers = @{
    'Content-Type' = 'application/json'
}
$body = @{
    SystemKbn = '22'
    ApiKey = 'PROD_ADMS_DBS_8341E93326A2A9BA2EC51245D243F67D'
    CountryCd = 'JPN'
    CompanyCd = '0001'
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3000/api/GetScanlessInstListForInstReg" -Method POST -Headers $headers -Body $body
```

## セキュリティ設定

### 1. 本番環境でのHTTPS化
```typescript
// src/environments/environment.prod.ts
export const environment = {
  production: true,
  defaultLang: 'ja-JP_dotst',
  apiBase: 'https://[バックエンドサーバーFQDN]/ace/API/',
  SystemKbn: '22',
  ApiKey: '[本番環境用APIキー]',
  CountryCd: 'JPN',
  CompanyCd: '0001',
  DateFormatString: '/',
  StatusCodeOK: '0000',
  ExecUser: "[実行ユーザーID]",
  PdfFileBaseUrl: "https://[バックエンドサーバーFQDN]/output/",
  ConfigFile: "assets/config/config.json"
};
```

### 2. CSP（Content Security Policy）設定
`src/index.html` にCSPヘッダーを追加：
```html
<meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self';">
```

## 連絡先
技術的な問題については、システム管理者またはIT部門にお問い合わせください。

## 技術仕様詳細

### フレームワーク・ライブラリ
- **Angular**: 5.2.0
- **Angular CLI**: 1.7.4
- **TypeScript**: 2.5.3
- **Node.js**: 8.x～10.x推奨
- **Bootstrap**: 3.3.7 (bootstrap-sass)
- **DevExtreme**: 19.1.6 (UIコンポーネント)
- **RxJS**: 5.5.6

### 主要な依存関係
- **認証**: ngx-cookie-service, angular2-uuid
- **UI**: ngx-bootstrap, ng-pick-datetime, ng2-toastr
- **PDF生成**: pdfmake, jspdf
- **国際化**: @ngx-translate/core
- **アニメーション**: @angular/animations, hammerjs

### アプリケーション構造
```
src/
├── app/
│   ├── components/
│   │   ├── auth/                    # 認証関連
│   │   ├── shipping-stock/          # スキャンレス出庫管理
│   │   └── return-instruction/      # 返品指示管理
│   ├── service/
│   │   ├── http/                    # HTTPサービス
│   │   └── common/                  # 共通サービス
│   └── shared/                      # 共有コンポーネント
├── environments/                    # 環境設定
└── assets/                         # 静的リソース
```

### 主要なルート
- `/login-with-user-and-password` - ログイン画面
- `/shipping-stock/inquiry` - 引上げ指示状況照会
- `/shipping-stock/scanlessManagement` - 引上げ指示登録
- `/shipping-stock/scanless-confirm` - 出庫状況確認
- `/return-instruction/*` - 返品指示関連

## 補足
このアプリケーションは、スキャンレス（バーコードスキャンなし）での在庫管理に特化したシステムです。商品コードの手入力による効率的な在庫管理を実現し、リアルタイムでの在庫状況把握が可能です。

### スキャンレス機能の特徴
- **商品コード手入力**: バーコードリーダー不要の在庫管理
- **リアルタイム更新**: 即座にシステム在庫を反映
- **指示ベース管理**: 出庫指示に基づく効率的な作業フロー
- **ゼロ確定機能**: 未処理商品の一括ゼロ確定
- **エラーハンドリング**: 商品マスター不整合の検出と対応
